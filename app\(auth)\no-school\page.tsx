'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { School, AlertCircle, LogOut, UserPlus } from 'lucide-react'

export default function NoSchoolPage() {
  const { user, signOut } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(false)

  const handleLogout = async () => {
    setLoading(true)
    try {
      await signOut()
      router.push('/login')
    } catch (error) {
      console.error('Error signing out:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleRegisterSchool = () => {
    router.push('/register')
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-orange-100 flex items-center justify-center">
            <School className="h-6 w-6 text-orange-600" />
          </div>
          <CardTitle className="text-xl font-semibold text-gray-900">
            No School Found
          </CardTitle>
          <CardDescription className="text-gray-600">
            Your account is not associated with any school.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <Alert className="border-orange-200 bg-orange-50">
            <AlertCircle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              To access the school management system, you need to be associated with a school.
            </AlertDescription>
          </Alert>

          {user && (
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-sm text-gray-600">
                <strong>Current Account:</strong> {user.email}
              </p>
              <p className="text-sm text-gray-600">
                <strong>User ID:</strong> {user.id}
              </p>
            </div>
          )}

          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">What you can do:</h4>
            <ul className="text-sm text-gray-600 space-y-2">
              <li className="flex items-start">
                <span className="mr-2">1.</span>
                <span>Register a new school if you're setting up a new institution</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">2.</span>
                <span>Contact your school administrator to add you to an existing school</span>
              </li>
              <li className="flex items-start">
                <span className="mr-2">3.</span>
                <span>Sign out and use a different account that's already associated with a school</span>
              </li>
            </ul>
          </div>

          <div className="flex flex-col gap-3 pt-4">
            <Button 
              onClick={handleRegisterSchool}
              className="w-full"
              disabled={loading}
            >
              <UserPlus className="mr-2 h-4 w-4" />
              Register New School
            </Button>
            
            <Button 
              variant="outline" 
              onClick={handleLogout}
              className="w-full"
              disabled={loading}
            >
              <LogOut className="mr-2 h-4 w-4" />
              {loading ? 'Signing Out...' : 'Sign Out'}
            </Button>
          </div>

          <div className="pt-4 border-t">
            <p className="text-xs text-gray-500 text-center">
              If you believe this is an error, please contact support or your system administrator.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
