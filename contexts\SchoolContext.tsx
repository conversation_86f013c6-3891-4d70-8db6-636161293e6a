'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './AuthContext'
import { getCurrentUserSchool, School } from '@/lib/supabase'
import { createClient } from '@/utils/supabase/client'

interface SchoolContextType {
  school: School | null
  loading: boolean
  refreshSchool: () => Promise<void>
}

const SchoolContext = createContext<SchoolContextType | undefined>(undefined)

export function SchoolProvider({ children }: { children: React.ReactNode }) {
  const [school, setSchool] = useState<School | null>(null)
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()
  const supabase = createClient()

  const refreshSchool = async () => {
    if (!user) {
      setSchool(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)

      // Use server-side API endpoint instead of client-side query
      const response = await fetch('/api/user-school', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Error fetching school:', errorData.error)
        setSchool(null)
        return
      }

      const data = await response.json()
      if (data.success && data.school) {
        setSchool(data.school)
      } else {
        setSchool(null)
      }
    } catch (error) {
      console.error('Error fetching school:', error)
      setSchool(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    refreshSchool()
  }, [user])

  const value = {
    school,
    loading,
    refreshSchool,
  }

  return (
    <SchoolContext.Provider value={value}>
      {children}
    </SchoolContext.Provider>
  )
}

export function useSchool() {
  const context = useContext(SchoolContext)
  if (context === undefined) {
    throw new Error('useSchool must be used within a SchoolProvider')
  }
  return context
}
