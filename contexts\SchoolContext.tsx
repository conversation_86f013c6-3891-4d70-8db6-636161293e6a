'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { useAuth } from './AuthContext'
import { getCurrentUserSchool, School } from '@/lib/supabase'

interface SchoolContextType {
  school: School | null
  loading: boolean
  refreshSchool: () => Promise<void>
}

const SchoolContext = createContext<SchoolContextType | undefined>(undefined)

export function SchoolProvider({ children }: { children: React.ReactNode }) {
  const [school, setSchool] = useState<School | null>(null)
  const [loading, setLoading] = useState(true)
  const { user } = useAuth()

  const refreshSchool = async () => {
    if (!user) {
      setSchool(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const userSchool = await getCurrentUserSchool()
      setSchool(userSchool)
    } catch (error) {
      console.error('Error fetching school:', error)
      setSchool(null)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    refreshSchool()
  }, [user])

  const value = {
    school,
    loading,
    refreshSchool,
  }

  return (
    <SchoolContext.Provider value={value}>
      {children}
    </SchoolContext.Provider>
  )
}

export function useSchool() {
  const context = useContext(SchoolContext)
  if (context === undefined) {
    throw new Error('useSchool must be used within a SchoolProvider')
  }
  return context
}
