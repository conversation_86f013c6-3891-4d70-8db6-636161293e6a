import { create } from 'zustand'
import { Student, StudentFormData } from '@/lib/supabase'
import { validateStudentData, handleError, rateLimiter } from '@/lib/security'
import { createClient } from '@/utils/supabase/client'

interface StudentStore {
  students: Student[]
  loading: boolean
  error: string | null
  fetchStudents: () => Promise<void>
  addStudent: (student: StudentFormData) => Promise<void>
  updateStudent: (id: string, updates: Partial<StudentFormData>) => Promise<void>
  deleteStudent: (id: string) => Promise<void>
  clearError: () => void
}

// Rate limiting identifier (in production, use user ID or IP)
const getRateLimitId = () => 'admin-user' // Simplified for demo

export const useStudentStore = create<StudentStore>((set, get) => {
  const supabase = createClient()

  return {
    students: [],
    loading: false,
    error: null,

    fetchStudents: async () => {
      set({ loading: true, error: null })
      try {
        const { data, error } = await supabase
          .from('students')
          .select('id, name, email, grade, role, created_at')
          .eq('role', 'student')
          .order('created_at', { ascending: false })

        if (error) {
          throw new Error(error.message)
        }

        set({ students: data || [], loading: false })
      } catch (error) {
        set({
          error: handleError(error),
          loading: false
        })
      }
    },

  addStudent: async (student: StudentFormData) => {
    set({ loading: true, error: null })
    try {
      // Rate limiting check
      if (!rateLimiter.isAllowed(getRateLimitId())) {
        throw new Error('Too many requests. Please wait before trying again.')
      }

      // Validate and sanitize input data
      const validation = validateStudentData(student)
      if (!validation.isValid) {
        const errorMessage = Object.values(validation.errors)[0]
        throw new Error(errorMessage)
      }

      const sanitizedStudent = {
        ...validation.data,
        role: 'student'
      }

      // Create student user account in Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: sanitizedStudent.email,
        password: 'temporary-password-123', // In production, send invite link instead
        options: {
          data: {
            name: sanitizedStudent.name,
            role: 'student'
          }
        }
      })

      if (authError) {
        throw new Error(`Authentication error: ${authError.message}`)
      }

      // Insert student record
      const { error: insertError } = await supabase
        .from('students')
        .insert([{
          ...sanitizedStudent,
          user_id: authData.user?.id
        }])

      if (insertError) {
        throw new Error(insertError.message)
      }

      // Refresh the students list
      await get().fetchStudents()
      set({ loading: false })
    } catch (error) {
      set({
        error: handleError(error),
        loading: false
      })
    }
  },

  updateStudent: async (id: string, updates: Partial<StudentFormData>) => {
    set({ loading: true, error: null })
    try {
      // Rate limiting check
      if (!rateLimiter.isAllowed(getRateLimitId())) {
        throw new Error('Too many requests. Please wait before trying again.')
      }

      // Create full student data for validation (merge with existing if partial update)
      const currentStudent = get().students.find(s => s.id === id)
      if (!currentStudent) {
        throw new Error('Student not found')
      }

      const fullData = {
        name: updates.name || currentStudent.name,
        email: updates.email || currentStudent.email,
        grade: updates.grade || currentStudent.grade
      }

      // Validate and sanitize
      const validation = validateStudentData(fullData)
      if (!validation.isValid) {
        const errorMessage = Object.values(validation.errors)[0]
        throw new Error(errorMessage)
      }

      // Only include fields that were actually updated
      const sanitizedUpdates: any = {}
      if (updates.name) sanitizedUpdates.name = validation.data.name
      if (updates.email) sanitizedUpdates.email = validation.data.email
      if (updates.grade) sanitizedUpdates.grade = validation.data.grade

      const { error } = await supabase
        .from('students')
        .update(sanitizedUpdates)
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }

      // Refresh the students list
      await get().fetchStudents()
      set({ loading: false })
    } catch (error) {
      set({
        error: handleError(error),
        loading: false
      })
    }
  },

  deleteStudent: async (id: string) => {
    set({ loading: true, error: null })
    try {
      // Rate limiting check
      if (!rateLimiter.isAllowed(getRateLimitId())) {
        throw new Error('Too many requests. Please wait before trying again.')
      }

      const { error } = await supabase
        .from('students')
        .delete()
        .eq('id', id)

      if (error) {
        throw new Error(error.message)
      }

      // Refresh the students list
      await get().fetchStudents()
      set({ loading: false })
    } catch (error) {
      set({
        error: handleError(error),
        loading: false
      })
    }
  },

    clearError: () => set({ error: null })
  }
})
