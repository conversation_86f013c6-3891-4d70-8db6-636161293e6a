'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/utils/supabase/server'
import { generateStudentPassword, generateStudentId, hashPassword } from '@/lib/password-utils'
import { validateStudentData } from '@/lib/security'

export interface StudentRegistrationData {
  name: string
  email: string
  grade: string
  parentName: string
  parentMobile: string
  address: string
  dateOfBirth: string
}

export interface StudentRegistrationResult {
  success?: boolean
  error?: string
  studentId?: string
  temporaryPassword?: string
  message?: string
}

export async function registerStudent(formData: FormData): Promise<StudentRegistrationResult> {
  try {
    const supabase = await createClient()
    
    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return { error: 'Unauthorized. Please log in as an admin.' }
    }

    // Get admin's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      return { error: 'Admin school not found. Please contact support.' }
    }

    // Extract form data
    const studentData: StudentRegistrationData = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      grade: formData.get('grade') as string,
      parentName: formData.get('parentName') as string,
      parentMobile: formData.get('parentMobile') as string,
      address: formData.get('address') as string,
      dateOfBirth: formData.get('dateOfBirth') as string,
    }

    // Validate student data
    const validation = validateStudentData(studentData)
    if (!validation.isValid) {
      const errorMessage = Object.values(validation.errors)[0]
      return { error: errorMessage }
    }

    // Generate student credentials
    const temporaryPassword = generateStudentPassword(studentData.name, studentData.dateOfBirth)
    const studentId = generateStudentId(studentData.name)
    
    console.log('Generated credentials:', { studentId, temporaryPassword: temporaryPassword.substring(0, 3) + '***' })

    // Create student user account in Supabase Auth
    const { data: authData, error: authCreateError } = await supabase.auth.admin.createUser({
      email: studentData.email,
      password: temporaryPassword,
      email_confirm: true, // Auto-confirm email for students
      user_metadata: {
        name: studentData.name,
        role: 'student',
        student_id: studentId,
        school_id: schoolAdmin.school_id
      }
    })

    if (authCreateError) {
      console.error('Auth creation error:', authCreateError)
      return { error: `Failed to create student account: ${authCreateError.message}` }
    }

    if (!authData.user) {
      return { error: 'Failed to create student account. No user data returned.' }
    }

    // Hash the password for storage in our database
    const hashedPassword = await hashPassword(temporaryPassword)

    // Insert student record into students table
    const { error: insertError } = await supabase
      .from('students')
      .insert([{
        user_id: authData.user.id,
        student_id: studentId,
        name: validation.data.name,
        email: validation.data.email,
        grade: validation.data.grade,
        parent_name: validation.data.parentName,
        parent_mobile: validation.data.parentMobile,
        address: validation.data.address,
        date_of_birth: validation.data.dateOfBirth,
        school_id: schoolAdmin.school_id,
        role: 'student',
        temporary_password_hash: hashedPassword,
        password_changed: false,
        created_by: user.id
      }])

    if (insertError) {
      console.error('Database insert error:', insertError)
      
      // Clean up the auth user if database insert fails
      try {
        await supabase.auth.admin.deleteUser(authData.user.id)
      } catch (cleanupError) {
        console.error('Failed to cleanup auth user:', cleanupError)
      }
      
      return { error: `Failed to save student record: ${insertError.message}` }
    }

    // Revalidate the students page
    revalidatePath('/students')

    return {
      success: true,
      studentId,
      temporaryPassword,
      message: `Student ${studentData.name} has been successfully registered with ID: ${studentId}`
    }

  } catch (error: any) {
    console.error('Student registration error:', error)
    return { error: `Registration failed: ${error.message || 'Unknown error occurred'}` }
  }
}

export async function getStudents() {
  try {
    const supabase = await createClient()
    
    // Get current user (admin)
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    // Get admin's school
    const { data: schoolAdmin, error: schoolError } = await supabase
      .from('school_admins')
      .select('school_id')
      .eq('user_id', user.id)
      .single()

    if (schoolError || !schoolAdmin) {
      throw new Error('School not found')
    }

    // Fetch students for this school
    const { data: students, error: studentsError } = await supabase
      .from('students')
      .select(`
        id,
        student_id,
        name,
        email,
        grade,
        parent_name,
        parent_mobile,
        address,
        date_of_birth,
        password_changed,
        created_at
      `)
      .eq('school_id', schoolAdmin.school_id)
      .eq('role', 'student')
      .order('created_at', { ascending: false })

    if (studentsError) {
      throw new Error(studentsError.message)
    }

    return students || []

  } catch (error: any) {
    console.error('Error fetching students:', error)
    throw error
  }
}
