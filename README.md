# School Management Admin Panel

This is the admin panel for the School Management System, built with Next.js, TypeScript, Supabase, and Zustand. This implements Phase 1 (Core Functionality) of the school management app, specifically the Student Information Management feature.

## Features Implemented

### Student Information Management (Phase 1, Sprint 1)
- ✅ **Admin CRUD Operations**: Create, read, update, and delete student records
- ✅ **Secure Input Handling**: Input sanitization and validation to prevent XSS attacks
- ✅ **Form Validation**: Email validation, required field checks, and data formatting
- ✅ **Error Handling**: Comprehensive error handling with user-friendly messages
- ✅ **Rate Limiting**: Basic rate limiting to prevent API abuse
- ✅ **Responsive UI**: Modern, responsive interface using shadcn/ui components
- ✅ **Real-time Updates**: Automatic refresh of student list after operations

### Security Features
- Input sanitization using `sanitize-html`
- Email format validation
- Required field validation
- Rate limiting for API requests
- Error handling with secure error messages
- TypeScript for type safety

## Technology Stack

- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: Zustand
- **Backend**: Supabase (PostgreSQL, Auth, Real-time)
- **Security**: Input sanitization, validation, rate limiting

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd school_management_admin
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
Create a `.env.local` file in the root directory:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
```

4. Set up Supabase database:
Create the following table in your Supabase database:
```sql
CREATE TABLE students (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES auth.users(id),
  name TEXT NOT NULL,
  email TEXT NOT NULL UNIQUE,
  grade TEXT,
  role TEXT DEFAULT 'student',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

5. Set up Row Level Security (RLS) policies:
```sql
-- Enable RLS
ALTER TABLE students ENABLE ROW LEVEL SECURITY;

-- Admin full access (you'll need to implement role-based auth)
CREATE POLICY admin_crud ON students
  FOR ALL
  TO authenticated
  USING (true); -- Simplified for demo - implement proper role checking

-- Student view own data
CREATE POLICY student_view ON students
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);
```

6. Run the development server:
```bash
npm run dev
```

7. Open [http://localhost:3000](http://localhost:3000) to see the admin panel.

## Project Structure

```
├── app/                    # Next.js app directory
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page with Student Management
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   └── StudentManagement.tsx # Main student management component
├── lib/                   # Utility libraries
│   ├── supabase.ts       # Supabase client configuration
│   ├── security.ts       # Security utilities
│   └── utils.ts          # General utilities
├── stores/               # Zustand stores
│   └── studentStore.ts   # Student management store
└── blueprint/            # Project documentation
    ├── School Management App Phase-Wise Development Plan with Test Cases.markdown
    ├── School Management App - First Feature Development Plan.markdown
    └── Cybersecurity Checks for School Management App.markdown
```

## Usage

### Adding Students
1. Fill in the student form with name, email, and grade
2. Click "Add Student" to create the record
3. The student will appear in the table below

### Editing Students
1. Click the edit button (pencil icon) next to a student
2. Modify the fields inline in the table
3. Click "Save" to confirm changes or "Cancel" to discard

### Deleting Students
1. Click the delete button (trash icon) next to a student
2. Confirm the deletion in the popup dialog

## Security Considerations

This implementation follows the cybersecurity requirements from the blueprint:

- **Input Sanitization**: All user inputs are sanitized to prevent XSS attacks
- **Validation**: Email format and required field validation
- **Rate Limiting**: Basic rate limiting to prevent API abuse
- **Error Handling**: Secure error messages that don't expose sensitive information
- **Type Safety**: TypeScript ensures type safety throughout the application

## Next Steps

This completes Phase 1, Sprint 1 of the development plan. Next implementations should include:

1. **Authentication System**: Implement proper admin authentication
2. **Role-Based Access Control**: Enhance RLS policies for different user roles
3. **Attendance Tracking**: Add attendance management features
4. **Gradebook**: Implement grade management system
5. **Academic Calendar**: Add calendar functionality

## Testing

To test the Student Information Management feature:

1. **Add Student Test**: Try adding a student with valid data
2. **Validation Test**: Try submitting empty fields or invalid email
3. **XSS Prevention Test**: Try entering `<script>alert('test')</script>` in name field
4. **Edit Test**: Edit an existing student's information
5. **Delete Test**: Delete a student record

## Contributing

Follow the development plan outlined in the blueprint documents for consistent implementation of new features.

## License

This project is part of the School Management System development plan.
