import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// Legacy client - kept for backward compatibility
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database tables
export interface School {
  id: string
  name: string
  email: string
  phone?: string
  address?: string
  website?: string
  logo_url?: string
  subscription_plan: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface Student {
  id: string
  school_id: string
  user_id?: string
  name: string
  email: string
  grade: string
  parent_name: string
  parent_mobile: string
  address: string
  date_of_birth: string
  role: string
  created_at: string
}

export interface SchoolAdmin {
  id: string
  user_id: string
  school_id: string
  role: string
  is_primary: boolean
  created_at: string
}

export interface StudentFormData {
  name: string
  email: string
  grade: string
  parentName: string
  parentMobile: string
  address: string
  dateOfBirth: string
}

export interface SchoolRegistrationData {
  schoolName: string
  schoolEmail: string
  schoolPhone?: string
  schoolAddress?: string
  adminName: string
  adminEmail: string
  adminPassword: string
}

// Database functions - these should be called from server components or server actions
export async function getCurrentUserSchool(supabaseClient: any): Promise<School | null> {
  try {
    const { data: { user } } = await supabaseClient.auth.getUser()

    if (!user) {
      return null
    }

    const { data, error } = await supabaseClient
      .from('school_admins')
      .select(`
        school_id,
        schools!inner (
          id,
          name,
          email,
          phone,
          address,
          website,
          logo_url,
          subscription_plan,
          is_active,
          created_at,
          updated_at
        )
      `)
      .eq('user_id', user.id)
      .single()

    if (error) {
      console.error('Error fetching user school:', error)
      return null
    }

    return data?.schools as School || null
  } catch (error) {
    console.error('Error in getCurrentUserSchool:', error)
    return null
  }
}

export async function getStudents(supabaseClient: any): Promise<Student[]> {
  const { data, error } = await supabaseClient
    .from('students')
    .select('*')
    .order('created_at', { ascending: false })

  if (error) {
    console.error('Error fetching students:', error)
    throw error
  }

  return data || []
}

export async function addStudent(supabaseClient: any, studentData: StudentFormData): Promise<Student> {
  // Get the current user's school
  const school = await getCurrentUserSchool(supabaseClient)
  if (!school) {
    throw new Error('No school found for current user')
  }

  const { data, error } = await supabaseClient
    .from('students')
    .insert([{
      school_id: school.id,
      name: studentData.name,
      email: studentData.email,
      grade: studentData.grade,
      parent_name: studentData.parentName,
      parent_mobile: studentData.parentMobile,
      address: studentData.address,
      date_of_birth: studentData.dateOfBirth,
      role: 'student'
    }])
    .select()
    .single()

  if (error) {
    console.error('Error adding student:', error)
    throw error
  }

  return data
}

export async function updateStudent(supabaseClient: any, id: string, studentData: Partial<StudentFormData>): Promise<Student> {
  const updateData: any = {}

  if (studentData.name) updateData.name = studentData.name
  if (studentData.email) updateData.email = studentData.email
  if (studentData.grade) updateData.grade = studentData.grade
  if (studentData.parentName) updateData.parent_name = studentData.parentName
  if (studentData.parentMobile) updateData.parent_mobile = studentData.parentMobile
  if (studentData.address) updateData.address = studentData.address
  if (studentData.dateOfBirth) updateData.date_of_birth = studentData.dateOfBirth

  const { data, error } = await supabaseClient
    .from('students')
    .update(updateData)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating student:', error)
    throw error
  }

  return data
}

export async function deleteStudent(supabaseClient: any, id: string): Promise<void> {
  const { error } = await supabaseClient
    .from('students')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting student:', error)
    throw error
  }
}

// School registration function
export async function registerSchool(registrationData: SchoolRegistrationData): Promise<{ school: School; user: any }> {
  // First, create the user account
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email: registrationData.adminEmail,
    password: registrationData.adminPassword,
    options: {
      data: {
        name: registrationData.adminName,
        role: 'school_admin'
      }
    }
  })

  if (authError) {
    console.error('Error creating user:', authError)
    throw authError
  }

  if (!authData.user) {
    throw new Error('Failed to create user account')
  }

  // Create the school
  const { data: schoolData, error: schoolError } = await supabase
    .from('schools')
    .insert([{
      name: registrationData.schoolName,
      email: registrationData.schoolEmail,
      phone: registrationData.schoolPhone,
      address: registrationData.schoolAddress,
    }])
    .select()
    .single()

  if (schoolError) {
    console.error('Error creating school:', schoolError)
    throw schoolError
  }

  // Link the user to the school as primary admin
  const { error: adminError } = await supabase
    .from('school_admins')
    .insert([{
      user_id: authData.user.id,
      school_id: schoolData.id,
      role: 'admin',
      is_primary: true
    }])

  if (adminError) {
    console.error('Error creating school admin:', adminError)
    throw adminError
  }

  return { school: schoolData, user: authData.user }
}
