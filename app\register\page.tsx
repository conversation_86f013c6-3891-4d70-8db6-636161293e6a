'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'

import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, AlertCircle, School, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function RegisterPage() {
  const [formData, setFormData] = useState({
    schoolName: '',
    schoolEmail: '',
    schoolPhone: '',
    schoolAddress: '',
    adminName: '',
    adminEmail: '',
    adminPassword: '',
    confirmPassword: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [passwordErrors, setPasswordErrors] = useState<string[]>([])
  const [showPasswordRules, setShowPasswordRules] = useState(false)
  
  const { user } = useAuth()
  const router = useRouter()

  // Redirect if already logged in
  useEffect(() => {
    if (user) {
      router.push('/dashboard')
    }
  }, [user, router])

  // Password validation rules
  const validatePassword = (password: string): string[] => {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    if (password.length > 128) {
      errors.push('Password must be less than 128 characters')
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character (!@#$%^&*)')
    }
    if (/(.)\1{2,}/.test(password)) {
      errors.push('Password cannot contain more than 2 consecutive identical characters')
    }
    if (/^(.{0,2})\1+$/.test(password)) {
      errors.push('Password cannot be a simple repeating pattern')
    }

    // Check for common weak passwords
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ]
    if (commonPasswords.includes(password.toLowerCase())) {
      errors.push('Password is too common. Please choose a more unique password')
    }

    return errors
  }

  const getPasswordStrength = (password: string): { score: number; label: string; color: string } => {
    if (password.length === 0) return { score: 0, label: '', color: '' }

    let score = 0
    const errors = validatePassword(password)

    // Base score from length
    if (password.length >= 8) score += 20
    if (password.length >= 12) score += 10
    if (password.length >= 16) score += 10

    // Character variety
    if (/[a-z]/.test(password)) score += 10
    if (/[A-Z]/.test(password)) score += 10
    if (/\d/.test(password)) score += 10
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 15

    // Complexity bonus
    const uniqueChars = new Set(password).size
    if (uniqueChars >= password.length * 0.7) score += 15

    // Penalty for errors
    score -= errors.length * 10

    if (score < 30) return { score, label: 'Very Weak', color: 'bg-red-500' }
    if (score < 50) return { score, label: 'Weak', color: 'bg-orange-500' }
    if (score < 70) return { score, label: 'Fair', color: 'bg-yellow-500' }
    if (score < 85) return { score, label: 'Good', color: 'bg-blue-500' }
    return { score, label: 'Strong', color: 'bg-green-500' }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    setFormData({
      ...formData,
      [name]: value
    })

    // Validate password in real-time
    if (name === 'adminPassword') {
      const errors = validatePassword(value)
      setPasswordErrors(errors)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    // Validation
    if (!formData.schoolName.trim() || !formData.schoolEmail.trim() ||
        !formData.adminName.trim() || !formData.adminEmail.trim() ||
        !formData.adminPassword.trim()) {
      setError('Please fill in all required fields')
      setLoading(false)
      return
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.schoolEmail)) {
      setError('Please enter a valid school email address')
      setLoading(false)
      return
    }
    if (!emailRegex.test(formData.adminEmail)) {
      setError('Please enter a valid admin email address')
      setLoading(false)
      return
    }

    // Password validation
    const passwordValidationErrors = validatePassword(formData.adminPassword)
    if (passwordValidationErrors.length > 0) {
      setError('Password does not meet security requirements: ' + passwordValidationErrors.join(', '))
      setLoading(false)
      return
    }

    if (formData.adminPassword !== formData.confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    // Check password strength
    const strength = getPasswordStrength(formData.adminPassword)
    if (strength.score < 50) {
      setError('Password is too weak. Please choose a stronger password.')
      setLoading(false)
      return
    }

    try {
      const response = await fetch('/api/register-school', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          schoolName: formData.schoolName,
          schoolEmail: formData.schoolEmail,
          schoolPhone: formData.schoolPhone,
          schoolAddress: formData.schoolAddress,
          adminName: formData.adminName,
          adminEmail: formData.adminEmail,
          adminPassword: formData.adminPassword
        })
      })

      const result = await response.json()

      if (!response.ok) {
        setError(result.error || 'Failed to register school')
        setLoading(false)
        return
      }

      setSuccess(true)
      setTimeout(() => {
        router.push('/login?message=Registration successful! Please sign in.')
      }, 2000)
    } catch (err: any) {
      console.error('Registration error:', err)
      setError(err.message || 'Failed to register school')
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <School className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-2">
                  Registration Successful!
                </h2>
                <p className="text-gray-600">
                  Your school has been registered successfully. Redirecting to login...
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-2xl w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
              <School className="h-8 w-8 text-white" />
            </div>
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            Register Your School
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            Join Next Gen School Management System
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>School Registration</CardTitle>
            <CardDescription>
              Create an account for your school and set up your admin access
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert variant="destructive" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              {/* School Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">School Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="schoolName" className="block text-sm font-medium text-gray-700 mb-2">
                      School Name *
                    </label>
                    <Input
                      id="schoolName"
                      name="schoolName"
                      type="text"
                      placeholder="Enter school name"
                      value={formData.schoolName}
                      onChange={handleChange}
                      required
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <label htmlFor="schoolEmail" className="block text-sm font-medium text-gray-700 mb-2">
                      School Email *
                    </label>
                    <Input
                      id="schoolEmail"
                      name="schoolEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.schoolEmail}
                      onChange={handleChange}
                      required
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="schoolPhone" className="block text-sm font-medium text-gray-700 mb-2">
                      School Phone
                    </label>
                    <Input
                      id="schoolPhone"
                      name="schoolPhone"
                      type="tel"
                      placeholder="Enter phone number"
                      value={formData.schoolPhone}
                      onChange={handleChange}
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <label htmlFor="schoolAddress" className="block text-sm font-medium text-gray-700 mb-2">
                      School Address
                    </label>
                    <Input
                      id="schoolAddress"
                      name="schoolAddress"
                      type="text"
                      placeholder="Enter school address"
                      value={formData.schoolAddress}
                      onChange={handleChange}
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>

              {/* Admin Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-gray-900">Admin Account</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="adminName" className="block text-sm font-medium text-gray-700 mb-2">
                      Admin Name *
                    </label>
                    <Input
                      id="adminName"
                      name="adminName"
                      type="text"
                      placeholder="Enter admin name"
                      value={formData.adminName}
                      onChange={handleChange}
                      required
                      disabled={loading}
                    />
                  </div>

                  <div>
                    <label htmlFor="adminEmail" className="block text-sm font-medium text-gray-700 mb-2">
                      Admin Email *
                    </label>
                    <Input
                      id="adminEmail"
                      name="adminEmail"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.adminEmail}
                      onChange={handleChange}
                      required
                      disabled={loading}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label htmlFor="adminPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      Password *
                    </label>
                    <Input
                      id="adminPassword"
                      name="adminPassword"
                      type="password"
                      placeholder="Enter a strong password"
                      value={formData.adminPassword}
                      onChange={handleChange}
                      onFocus={() => setShowPasswordRules(true)}
                      required
                      disabled={loading}
                      className={passwordErrors.length > 0 ? 'border-red-300' : ''}
                    />

                    {/* Password Strength Indicator */}
                    {formData.adminPassword && (
                      <div className="mt-2">
                        <div className="flex items-center justify-between text-sm mb-1">
                          <span>Password Strength:</span>
                          <span className={`font-medium ${
                            getPasswordStrength(formData.adminPassword).score >= 70 ? 'text-green-600' :
                            getPasswordStrength(formData.adminPassword).score >= 50 ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                            {getPasswordStrength(formData.adminPassword).label}
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrength(formData.adminPassword).color}`}
                            style={{ width: `${Math.min(getPasswordStrength(formData.adminPassword).score, 100)}%` }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {/* Password Rules */}
                    {showPasswordRules && (
                      <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                        <h4 className="text-sm font-medium text-blue-800 mb-2">Password Requirements:</h4>
                        <ul className="text-xs text-blue-700 space-y-1">
                          <li className={`flex items-center ${formData.adminPassword.length >= 8 ? 'text-green-600' : ''}`}>
                            <span className="mr-2">{formData.adminPassword.length >= 8 ? '✓' : '•'}</span>
                            At least 8 characters long
                          </li>
                          <li className={`flex items-center ${/[a-z]/.test(formData.adminPassword) ? 'text-green-600' : ''}`}>
                            <span className="mr-2">{/[a-z]/.test(formData.adminPassword) ? '✓' : '•'}</span>
                            One lowercase letter (a-z)
                          </li>
                          <li className={`flex items-center ${/[A-Z]/.test(formData.adminPassword) ? 'text-green-600' : ''}`}>
                            <span className="mr-2">{/[A-Z]/.test(formData.adminPassword) ? '✓' : '•'}</span>
                            One uppercase letter (A-Z)
                          </li>
                          <li className={`flex items-center ${/\d/.test(formData.adminPassword) ? 'text-green-600' : ''}`}>
                            <span className="mr-2">{/\d/.test(formData.adminPassword) ? '✓' : '•'}</span>
                            One number (0-9)
                          </li>
                          <li className={`flex items-center ${/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(formData.adminPassword) ? 'text-green-600' : ''}`}>
                            <span className="mr-2">{/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(formData.adminPassword) ? '✓' : '•'}</span>
                            One special character (!@#$%^&*)
                          </li>
                          <li className={`flex items-center ${!/(.)\1{2,}/.test(formData.adminPassword) && formData.adminPassword.length > 0 ? 'text-green-600' : ''}`}>
                            <span className="mr-2">{!/(.)\1{2,}/.test(formData.adminPassword) && formData.adminPassword.length > 0 ? '✓' : '•'}</span>
                            No more than 2 consecutive identical characters
                          </li>
                        </ul>
                      </div>
                    )}

                    {/* Password Errors */}
                    {passwordErrors.length > 0 && (
                      <div className="mt-2">
                        {passwordErrors.map((error, index) => (
                          <p key={index} className="text-sm text-red-600 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {error}
                          </p>
                        ))}
                      </div>
                    )}
                  </div>

                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      Confirm Password *
                    </label>
                    <Input
                      id="confirmPassword"
                      name="confirmPassword"
                      type="password"
                      placeholder="Confirm your password"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      required
                      disabled={loading}
                      className={formData.confirmPassword && formData.adminPassword !== formData.confirmPassword ? 'border-red-300' : ''}
                    />
                    {formData.confirmPassword && formData.adminPassword !== formData.confirmPassword && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Passwords do not match
                      </p>
                    )}
                    {formData.confirmPassword && formData.adminPassword === formData.confirmPassword && formData.confirmPassword.length > 0 && (
                      <p className="mt-1 text-sm text-green-600 flex items-center">
                        <span className="mr-1">✓</span>
                        Passwords match
                      </p>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={
                    loading ||
                    passwordErrors.length > 0 ||
                    !formData.adminPassword ||
                    formData.adminPassword !== formData.confirmPassword ||
                    getPasswordStrength(formData.adminPassword).score < 50
                  }
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Registering School...
                    </>
                  ) : (
                    'Register School'
                  )}
                </Button>

                <Button 
                  type="button" 
                  variant="outline" 
                  asChild
                  disabled={loading}
                >
                  <Link href="/login">
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back to Login
                  </Link>
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
