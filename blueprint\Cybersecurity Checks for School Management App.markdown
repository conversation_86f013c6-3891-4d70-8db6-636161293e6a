# Cybersecurity Checks for School Management App

This document outlines cybersecurity checks to prevent cyber attacks for a school management app with an admin web panel (React, JavaScript), student/teacher mobile apps (React Native with Expo), Supabase backend, and Zustand state management. The checks address threats like unauthorized access, data breaches, injection attacks, and DoS attacks, ensuring compliance with GDPR, FERPA, and industry standards. The measures are integrated into the phase-wise development plan from the previous response, with specific actions for each phase, including tools, techniques, and validation steps.

## Threat Model
Key threats to the app include:
- **Unauthorized Access:** Hackers attempting to access student or financial data.
- **Data Breaches:** Exposure of sensitive data (e.g., student records, payment details).
- **SQL Injection:** Malicious queries exploiting database vulnerabilities.
- **Cross-Site Scripting (XSS):** Injecting scripts into the web app.
- **Cross-Site Request Forgery (CSRF):** Unauthorized actions via forged requests.
- **DoS/DDoS Attacks:** Overloading the app to disrupt service.
- **Man-in-the-Middle (MITM):** Intercepting data in transit.
- **Credential Stuffing:** Using stolen credentials to gain access.
- **Mobile App Vulnerabilities:** Exploiting insecure storage or API calls.

## Cybersecurity Checks by Development Phase
The checks are aligned with the four-phase development plan (Phase 1: Core Functionality, Phase 2: Communication and Financial, Phase 3: Academic and Online Learning, Phase 4: Advanced Features and Compliance). Each phase includes preventive measures and validation steps to ensure security.

### Phase 1: Core Functionality (MVP) - Weeks 1-8
**Objective:** Secure the foundational features (student information, grades, attendance, admin management) against unauthorized access and basic attacks.

**Features Secured:**
- Student Information Management (Students: view-only, Teachers: update, Admins: full CRUD)
- Gradebook and Assessment Management
- Attendance Tracking
- Academic Calendar
- Security and Access Control
- User Management Tools
- Centralized Dashboards
- Cloud-Based Solutions
- Enhanced Mobile App

**Cybersecurity Checks:**
1. **Role-Based Access Control (RBAC) with Supabase RLS**
   - **Action:** Implement Supabase Row-Level Security (RLS) policies to restrict access:
     - Students: read-only access to their own data (`SELECT` on `students` where `user_id = auth.uid()`).
     - Teachers: update permissions for grades/attendance (`UPDATE` on `grades`, `attendance` for their classes).
     - Admins: full CRUD access (`INSERT`, `UPDATE`, `DELETE` on all tables).
   - **Tools:** Supabase Dashboard, PostgreSQL policies.
   - **Validation:** Test RLS policies by attempting unauthorized actions (e.g., student updating grades, teacher deleting student records).
2. **JWT Authentication**
   - **Action:** Use Supabase Authentication with JWT tokens for user login (email/password, Google OAuth).
     - Set short token expiration (e.g., 1 hour) with refresh tokens.
     - Validate tokens on every API request.
   - **Tools:** Supabase Auth, JWT library (jsonwebtoken).
   - **Validation:** Test invalid/expired token rejection, ensure Google OAuth works.
3. **Data Encryption**
   - **Action:** Enable AES-256 encryption for data at rest in Supabase PostgreSQL.
     - Use TLS for all API communications (Supabase enforces HTTPS).
   - **Tools:** Supabase encryption settings, HTTPS configuration.
   - **Validation:** Verify data is encrypted in database (e.g., check `pgcrypto` extension), test HTTPS-only access.
4. **Input Validation and Sanitization**
   - **Action:** Sanitize inputs in React and React Native to prevent XSS and injection attacks.
     - Use libraries like `sanitize-html` for admin panel inputs (e.g., student names).
     - Validate inputs on Supabase API endpoints (e.g., reject malformed emails).
   - **Tools:** `sanitize-html`, Supabase Edge Functions for server-side validation.
   - **Validation:** Test with malicious inputs (e.g., `<script>alert('xss')</script>`, SQL queries).
5. **Secure API Endpoints**
   - **Action:** Restrict Supabase REST API access using API keys and RLS.
     - Disable public access to sensitive tables (e.g., `students`, `grades`).
   - **Tools:** Supabase API key management.
   - **Validation:** Attempt API calls without authentication, verify 403 errors.

**Test Cases:**
1. **RLS Access Control**
   - **Test Case:** Student cannot update grades, teacher cannot delete student records.
   - **Steps:** Log in as student, attempt `UPDATE` on `grades`; log in as teacher, attempt `DELETE` on `students`.
   - **Expected Result:** 403 Forbidden errors.
2. **JWT Authentication**
   - **Test Case:** Invalid token rejected.
   - **Steps:** Send API request with expired/invalid JWT.
   - **Expected Result:** 401 Unauthorized error.
3. **XSS Prevention**
   - **Test Case:** Malicious script in input field blocked.
   - **Steps:** Submit `<script>alert('xss')</script>` in student name field.
   - **Expected Result:** Script sanitized, no alert executed.
4. **Data Encryption**
   - **Test Case:** Data encrypted in database.
   - **Steps:** Query `students` table directly, check for encrypted fields.
   - **Expected Result:** Data unreadable without decryption key.

**Deliverables:**
- Supabase RLS policies for all tables.
- JWT authentication setup.
- Encrypted database and HTTPS configuration.
- Input sanitization in React/React Native.

### Phase 2: Communication and Financial Enhancements - Weeks 9-14
**Objective:** Secure messaging and financial features against MITM, CSRF, and payment-related attacks.

**Features Secured:**
- Communication Tools (messaging, parent portals)
- Financial Management (fee collection, invoicing, Stripe)
- Data Analytics and Reporting (attendance, financials)
- Centralized Dashboards
- Enhanced Mobile App

**Cybersecurity Checks:**
1. **Secure Real-Time Messaging**
   - **Action:** Use Supabase Realtime (WebSocket) with TLS for secure messaging.
     - Implement rate limiting to prevent DoS attacks on WebSocket connections.
     - Validate message senders using JWT and RLS.
   - **Tools:** Supabase Realtime, WebSocket rate limiter.
   - **Validation:** Test messaging with invalid JWT, attempt rapid message spam.
2. **CSRF Protection**
   - **Action:** Implement CSRF tokens for admin panel forms (e.g., invoice creation).
     - Use Supabase Auth to validate requests.
   - **Tools:** `csrf-token` library for React, Supabase Auth.
   - **Validation:** Attempt form submission without CSRF token, verify rejection.
3. **Secure Payment Processing**
   - **Action:** Use Stripe’s secure payment intents for fee collection.
     - Store minimal payment data (e.g., only Stripe transaction IDs) in Supabase.
     - Enable PCI DSS compliance in Stripe settings.
   - **Tools:** Stripe API, Supabase Storage for receipts.
   - **Validation:** Test payment with invalid card, verify secure storage of transaction IDs.
4. **Secure Analytics Queries**
   - **Action:** Use parameterized SQL queries in Supabase to prevent SQL injection.
     - Restrict analytics queries to admin role via RLS.
   - **Tools:** Supabase SQL editor, RLS policies.
   - **Validation:** Attempt SQL injection (e.g., `1; DROP TABLE students`), verify rejection.

**Test Cases:**
1. **Secure Messaging**
   - **Test Case:** Unauthorized user cannot send messages.
   - **Steps:** Attempt WebSocket message with invalid JWT.
   - **Expected Result:** Message rejected, 401 error.
2. **CSRF Protection**
   - **Test Case:** Form submission without CSRF token fails.
   - **Steps:** Submit invoice form without token.
   - **Expected Result:** 403 Forbidden error.
3. **Payment Security**
   - **Test Case:** Payment data securely processed.
   - **Steps:** Process test payment, check database for stored data.
   - **Expected Result:** Only transaction ID stored, payment successful.
4. **SQL Injection Prevention**
   - **Test Case:** Malicious SQL query blocked.
   - **Steps:** Attempt analytics query with `1; DROP TABLE students`.
   - **Expected Result:** Query rejected, table intact.

**Deliverables:**
- Secure WebSocket messaging setup.
- CSRF protection for admin forms.
- Stripe payment integration with PCI compliance.
- Parameterized SQL queries for analytics.

### Phase 3: Academic and Online Learning Enhancements - Weeks 15-20
**Objective:** Secure online learning and academic features against API abuse and session hijacking.

**Features Secured:**
- Integrated Online Learning Platform
- Personalized Learning Paths
- Lesson Planning Tools
- Student Performance Analytics
- Timetable and Scheduling
- System Integration (Zoom, LMS)
- Data Analytics and Reporting

**Cybersecurity Checks:**
1. **Secure API Integrations**
   - **Action:** Secure Zoom and LMS integrations with OAuth 2.0 and API tokens.
     - Restrict API access to authorized users via Supabase RLS.
   - **Tools:** Zoom SDK, Google Classroom API, Supabase Auth.
   - **Validation:** Attempt API calls with invalid tokens, verify rejection.
2. **Session Management**
   - **Action:** Use Zustand with secure session storage in React Native (Expo SecureStore).
     - Implement auto-logout after inactivity (e.g., 15 minutes).
   - **Tools:** Expo SecureStore, Zustand middleware.
   - **Validation:** Test session persistence, verify auto-logout.
3. **AI Security**
   - **Action:** Secure AI-driven features (e.g., personalized learning) by validating inputs to TensorFlow.js models.
     - Use Supabase Edge Functions to isolate AI processing.
   - **Tools:** TensorFlow.js, Supabase Edge Functions.
   - **Validation:** Test AI with malicious inputs, verify no crashes or leaks.
4. **Rate Limiting APIs**
   - **Action:** Apply rate limiting to Supabase APIs to prevent abuse.
     - Use Supabase Edge Functions for custom rate-limiting logic.
   - **Tools:** Supabase Edge Functions, Redis for rate limiting.
   - **Validation:** Attempt rapid API calls, verify rate limit enforcement.

**Test Cases:**
1. **API Integration Security**
   - **Test Case:** Unauthorized Zoom API call rejected.
   - **Steps:** Attempt Zoom API call without valid token.
   - **Expected Result:** 401 Unauthorized error.
2. **Session Management**
   - **Test Case:** Auto-logout after inactivity.
   - **Steps:** Log in, wait 15 minutes, attempt action.
   - **Expected Result:** Session expired, user logged out.
3. **AI Input Validation**
   - **Test Case:** Malicious input to AI model blocked.
   - **Steps:** Send invalid data to personalized learning API.
   - **Expected Result:** Input rejected, no model crash.
4. **Rate Limiting**
   - **Test Case:** API abuse prevented.
   - **Steps:** Send 100 API calls in 1 minute.
   - **Expected Result:** Rate limit exceeded, 429 error returned.

**Deliverables:**
- Secure Zoom/LMS integrations.
- Secure session management with Zustand/Expo.
- Safe AI processing with Edge Functions.
- Rate-limited APIs.

### Phase 4: Advanced Features and Compliance - Weeks 21-26
**Objective:** Secure advanced features and ensure compliance with GDPR/FERPA through audits and biometric authentication.

**Features Secured:**
- AI and Automation (predictive analytics, chatbots)
- Disaster Preparedness and Safety
- Security and Access Control (biometric authentication)
- Enhanced Mobile App

**Cybersecurity Checks:**
1. **Biometric Authentication**
   - **Action:** Implement biometric login (fingerprint/face ID) using Expo Biometrics API.
     - Store biometric data securely (not in Supabase, use device storage).
   - **Tools:** Expo Biometrics API, Supabase Auth.
   - **Validation:** Test biometric login, attempt bypass with invalid credentials.
2. **Disaster Recovery**
   - **Action:** Enable Supabase backups and point-in-time recovery.
     - Implement emergency alert system with encrypted WebSocket channels.
   - **Tools:** Supabase Backups, WebSocket TLS.
   - **Validation:** Simulate database failure, test recovery; send encrypted alerts.
3. **Security Audits**
   - **Action:** Conduct penetration testing and vulnerability scans.
     - Use tools like OWASP ZAP and Burp Suite to identify weaknesses.
   - **Tools:** OWASP ZAP, Burp Suite.
   - **Validation:** Verify no critical vulnerabilities (e.g., XSS, SQL injection).
4. **GDPR/FERPA Compliance**
   - **Action:** Implement data minimization (store only necessary data).
     - Provide data export/deletion options for users.
     - Log all data access for audit trails.
   - **Tools:** Supabase audit logs, custom export functions.
   - **Validation:** Test data export/deletion, verify audit logs.

**Test Cases:**
1. **Biometric Authentication**
   - **Test Case:** Biometric login works, invalid attempts blocked.
   - **Steps:** Attempt login with valid/invalid biometrics.
   - **Expected Result:** Valid login succeeds, invalid fails.
2. **Disaster Recovery**
   - **Test Case:** Database recovery successful.
   - **Steps:** Simulate failure, restore from backup.
   - **Expected Result:** Data restored to previous state.
3. **Penetration Testing**
   - **Test Case:** No vulnerabilities found.
   - **Steps:** Run OWASP ZAP scan on app and APIs.
   - **Expected Result:** No high/critical issues reported.
4. **Compliance**
   - **Test Case:** User can export/delete data.
   - **Steps:** Request data export/deletion as student.
   - **Expected Result:** Data exported/deleted, audit log updated.

**Deliverables:**
- Biometric authentication in mobile app.
- Disaster recovery setup.
- Penetration test reports.
- GDPR/FERPA compliance features (export/deletion).

## Ongoing Security Practices
- **Regular Updates:** Patch React, React Native, Supabase client, and dependencies regularly (use `npm audit`).
- **Monitoring:** Use Supabase Logs and AWS CloudWatch (if hosted externally) for real-time monitoring of suspicious activity.
- **User Training:** Educate admins on security best practices (e.g., strong passwords, phishing awareness).
- **Incident Response:** Establish a plan for data breaches (e.g., notify users within 72 hours per GDPR).

## Sample Code (Secure API with RLS)
Below is a sample implementation of a secure Supabase API call for student data with RLS and JWT validation.

```javascript
// src/stores/studentStore.js (Zustand)
import { create } from 'zustand';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient('YOUR_SUPABASE_URL', 'YOUR_SUPABASE_KEY');

export const useStudentStore = create((set) => ({
  students: [],
  fetchStudents: async () => {
    const { data, error } = await supabase
      .from('students')
      .select('id, name, email, grade')
      .eq('role', 'student');
    if (error) {
      console.error('Error fetching students:', error);
      return;
    }
    set({ students: data });
  },
  addStudent: async (student) => {
    const { error } = await supabase
      .from('students')
      .insert([{ ...student, role: 'student' }]);
    if (error) {
      console.error('Error adding student:', error);
      return;
    }
    await useStudentStore.getState().fetchStudents();
  },
}));

// Supabase RLS Policy (SQL)
CREATE POLICY student_access ON students
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id OR auth.role() = 'admin');

CREATE POLICY teacher_update ON grades
  FOR UPDATE
  TO authenticated
  USING (auth.role() = 'teacher' AND class_id IN (
    SELECT class_id FROM teacher_classes WHERE teacher_id = auth.uid()
  ));
```

## Implementation Considerations
- **Phase Integration:** Embed security checks in each phase to avoid retrofitting.
- **Performance:** Balance security (e.g., encryption) with performance (e.g., optimize API calls).
- **Compliance:** Regularly audit for GDPR/FERPA compliance, especially in Phase 4.
- **Scalability:** Ensure security measures (e.g., rate limiting) support 10,000+ users.
- **Cost:** Use Supabase’s free tier for development, scale to paid for production security features.

## Benefits
- **Data Protection:** Prevents breaches of sensitive student and financial data.
- **User Trust:** Robust security builds confidence among students, teachers, and admins.
- **Compliance:** Meets GDPR/FERPA requirements, avoiding legal penalties.
- **Resilience:** Protects against DoS, injection, and other common attacks.

## Next Steps
- **Phase 1:** Implement RLS, JWT, and encryption immediately to secure core features.
- **Continuous Testing:** Run security tests after each sprint using OWASP ZAP.
- **Audit Planning:** Schedule third-party penetration testing for Phase 4.
- **User Education:** Develop security guides for admins to enforce best practices.

This cybersecurity plan ensures the school management app is protected against common cyber attacks, maintaining data integrity and user trust throughout development.