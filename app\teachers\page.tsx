import { AdminLayout } from '@/components/AdminLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { GraduationCap } from 'lucide-react'

export default function TeachersPage() {
  return (
    <AdminLayout title="Teacher Management">
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <GraduationCap className="h-5 w-5" />
              Teacher Management
            </CardTitle>
            <CardDescription>
              Manage teacher records, add new teachers, and update information
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <GraduationCap className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">Teacher Management Coming Soon</h3>
              <p className="text-muted-foreground">
                This feature will be available in the next development phase.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
