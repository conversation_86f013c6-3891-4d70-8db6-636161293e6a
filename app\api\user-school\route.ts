import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/utils/supabase/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()

    // Get current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()

    if (userError) {
      console.error('Error getting user:', userError)
      return NextResponse.json({ error: 'Unauthorized', details: userError }, { status: 401 })
    }

    if (!user) {
      console.log('No user found in session')
      return NextResponse.json({ error: 'No user found' }, { status: 401 })
    }

    console.log('Fetching school for user:', user.id, 'email:', user.email)

    // Get the school admin record and school details in one query
    const { data, error } = await supabase
      .from('school_admins')
      .select(`
        school_id,
        schools!inner (
          id,
          name,
          email,
          phone,
          address,
          website,
          logo_url,
          subscription_plan,
          is_active,
          created_at,
          updated_at
        )
      `)
      .eq('user_id', user.id)
      .single()

    if (error) {
      console.error('Error fetching school admin:', error)
      console.error('Query details - user_id:', user.id)

      // Try a simpler query to debug
      const { data: debugData, error: debugError } = await supabase
        .from('school_admins')
        .select('*')
        .eq('user_id', user.id)

      console.log('Debug query result:', debugData, 'error:', debugError)

      return NextResponse.json({
        error: 'School not found',
        details: error,
        userId: user.id,
        debugData
      }, { status: 404 })
    }

    if (!data || !data.schools) {
      console.log('No school data found for user:', user.id)
      return NextResponse.json({ error: 'No school associated with user' }, { status: 404 })
    }

    console.log('Successfully fetched school:', data.schools.name)
    
    return NextResponse.json({ 
      success: true, 
      school: data.schools 
    })

  } catch (error: any) {
    console.error('Error in user-school API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
