import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Create a Supabase client with service role key for admin operations
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  }
)

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      schoolName,
      schoolEmail,
      schoolPhone,
      schoolAddress,
      adminName,
      adminEmail,
      adminPassword
    } = body

    // Validation
    if (!schoolName || !schoolEmail || !adminName || !adminEmail || !adminPassword) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(schoolEmail)) {
      return NextResponse.json(
        { error: 'Invalid school email format' },
        { status: 400 }
      )
    }
    if (!emailRegex.test(adminEmail)) {
      return NextResponse.json(
        { error: 'Invalid admin email format' },
        { status: 400 }
      )
    }

    // Server-side password validation
    const validatePassword = (password: string): string[] => {
      const errors: string[] = []

      if (password.length < 8) {
        errors.push('Password must be at least 8 characters long')
      }
      if (password.length > 128) {
        errors.push('Password must be less than 128 characters')
      }
      if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter')
      }
      if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter')
      }
      if (!/\d/.test(password)) {
        errors.push('Password must contain at least one number')
      }
      if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
        errors.push('Password must contain at least one special character')
      }
      if (/(.)\1{2,}/.test(password)) {
        errors.push('Password cannot contain more than 2 consecutive identical characters')
      }

      // Check for common weak passwords
      const commonPasswords = [
        'password', '123456', '123456789', 'qwerty', 'abc123',
        'password123', 'admin', 'letmein', 'welcome', 'monkey'
      ]
      if (commonPasswords.includes(password.toLowerCase())) {
        errors.push('Password is too common. Please choose a more unique password')
      }

      return errors
    }

    const passwordErrors = validatePassword(adminPassword)
    if (passwordErrors.length > 0) {
      return NextResponse.json(
        { error: 'Password validation failed: ' + passwordErrors.join(', ') },
        { status: 400 }
      )
    }

    // Check if school email already exists
    const { data: existingSchool } = await supabaseAdmin
      .from('schools')
      .select('id')
      .eq('email', schoolEmail)
      .single()

    if (existingSchool) {
      return NextResponse.json(
        { error: 'A school with this email already exists' },
        { status: 400 }
      )
    }

    // Create the user account using admin client
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: adminEmail,
      password: adminPassword,
      email_confirm: true,
      user_metadata: {
        name: adminName,
        role: 'school_admin'
      }
    })

    if (authError) {
      console.error('Error creating user:', authError)
      return NextResponse.json(
        { error: authError.message || 'Failed to create user account' },
        { status: 400 }
      )
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'Failed to create user account' },
        { status: 400 }
      )
    }

    // Create the school
    const { data: schoolData, error: schoolError } = await supabaseAdmin
      .from('schools')
      .insert([{
        name: schoolName,
        email: schoolEmail,
        phone: schoolPhone || null,
        address: schoolAddress || null,
      }])
      .select()
      .single()

    if (schoolError) {
      console.error('Error creating school:', schoolError)
      // Clean up the user if school creation fails
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      return NextResponse.json(
        { error: schoolError.message || 'Failed to create school' },
        { status: 400 }
      )
    }

    // Link the user to the school as primary admin
    const { error: adminError } = await supabaseAdmin
      .from('school_admins')
      .insert([{
        user_id: authData.user.id,
        school_id: schoolData.id,
        role: 'admin',
        is_primary: true
      }])

    if (adminError) {
      console.error('Error creating school admin:', adminError)
      // Clean up the user and school if admin link creation fails
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      await supabaseAdmin.from('schools').delete().eq('id', schoolData.id)
      return NextResponse.json(
        { error: adminError.message || 'Failed to create admin link' },
        { status: 400 }
      )
    }

    return NextResponse.json({
      success: true,
      school: schoolData,
      message: 'School registered successfully'
    })

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
