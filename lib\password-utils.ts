import bcrypt from 'bcryptjs'

/**
 * Generate a secure temporary password for students
 * Format: FirstName + DDMM (from DOB) + random 2 digits
 * Example: John0512 + 47 = John051247
 */
export function generateStudentPassword(name: string, dateOfBirth: string): string {
  try {
    // Extract first name (capitalize first letter, lowercase rest)
    const firstName = name.split(' ')[0].toLowerCase()
    const capitalizedFirstName = firstName.charAt(0).toUpperCase() + firstName.slice(1)
    
    // Extract day and month from date of birth (YYYY-MM-DD format)
    const dob = new Date(dateOfBirth)
    const day = dob.getDate().toString().padStart(2, '0')
    const month = (dob.getMonth() + 1).toString().padStart(2, '0')
    
    // Generate 2 random digits
    const randomDigits = Math.floor(Math.random() * 90 + 10).toString()
    
    // Combine: FirstName + DDMM + RandomDigits
    const password = `${capitalizedFirstName}${day}${month}${randomDigits}`
    
    return password
  } catch (error) {
    console.error('Error generating password:', error)
    // Fallback to a simple random password
    return `Student${Math.floor(Math.random() * 100000).toString().padStart(5, '0')}`
  }
}

/**
 * Generate a more secure random password
 * Format: 3 uppercase + 3 lowercase + 2 numbers + 2 special chars
 */
export function generateSecurePassword(length: number = 10): string {
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowercase = 'abcdefghijklmnopqrstuvwxyz'
  const numbers = '0123456789'
  const special = '!@#$%^&*'
  
  let password = ''
  
  // Ensure at least one character from each category
  password += uppercase[Math.floor(Math.random() * uppercase.length)]
  password += lowercase[Math.floor(Math.random() * lowercase.length)]
  password += numbers[Math.floor(Math.random() * numbers.length)]
  password += special[Math.floor(Math.random() * special.length)]
  
  // Fill the rest randomly
  const allChars = uppercase + lowercase + numbers + special
  for (let i = password.length; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }
  
  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('')
}

/**
 * Hash a password using bcrypt
 */
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12 // High security
  return await bcrypt.hash(password, saltRounds)
}

/**
 * Verify a password against its hash
 */
export async function verifyPassword(password: string, hash: string): Promise<boolean> {
  return await bcrypt.compare(password, hash)
}

/**
 * Validate password strength
 */
export function validatePasswordStrength(password: string): {
  isValid: boolean
  errors: string[]
  score: number
} {
  const errors: string[] = []
  let score = 0
  
  // Length check
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long')
  } else {
    score += 1
  }
  
  // Uppercase check
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  } else {
    score += 1
  }
  
  // Lowercase check
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  } else {
    score += 1
  }
  
  // Number check
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number')
  } else {
    score += 1
  }
  
  // Special character check
  if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    errors.push('Password must contain at least one special character')
  } else {
    score += 1
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    score
  }
}

/**
 * Generate a student ID based on name and enrollment year
 */
export function generateStudentId(name: string, enrollmentYear?: number): string {
  const year = enrollmentYear || new Date().getFullYear()
  const initials = name
    .split(' ')
    .map(part => part.charAt(0).toUpperCase())
    .join('')
    .substring(0, 3)
  
  const randomNum = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
  
  return `${year}${initials}${randomNum}`
}

/**
 * Format password for display (show first 2 and last 2 characters)
 */
export function maskPassword(password: string): string {
  if (password.length <= 4) {
    return '*'.repeat(password.length)
  }
  
  const start = password.substring(0, 2)
  const end = password.substring(password.length - 2)
  const middle = '*'.repeat(password.length - 4)
  
  return `${start}${middle}${end}`
}
