import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, ArrowLeft } from 'lucide-react'

interface ErrorPageProps {
  searchParams: Promise<{ message?: string }>
}

export default async function ErrorPage({ searchParams }: ErrorPageProps) {
  const { message } = await searchParams
  const errorMessage = message || 'An unexpected error occurred'
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="h-8 w-8 text-red-600" />
              </div>
            </div>
            <CardTitle className="text-red-600">Authentication Error</CardTitle>
            <CardDescription>
              {errorMessage}
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <p className="text-sm text-gray-600">
              This could be due to:
            </p>
            <ul className="text-sm text-gray-600 text-left space-y-1">
              <li>• Invalid email or password</li>
              <li>• Expired or invalid confirmation link</li>
              <li>• Account not yet verified</li>
              <li>• Network connectivity issues</li>
              <li>• Missing required information</li>
            </ul>
            <div className="pt-4">
              <Button asChild className="w-full">
                <Link href="/login">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Login
                </Link>
              </Button>
            </div>
            <p className="text-xs text-gray-500">
              If the problem persists, please contact your system administrator.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
