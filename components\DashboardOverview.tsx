'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Users, GraduationCap, Users2, DollarSign, TrendingUp, TrendingDown } from 'lucide-react'

// Mock data for the dashboard
const statsData = [
  {
    title: 'Total Students',
    value: '0',
    icon: Users,
    color: 'bg-purple-100 text-purple-600',
    bgColor: 'bg-purple-50',
  },
  {
    title: 'Active Students',
    value: '0',
    icon: Users,
    color: 'bg-blue-100 text-blue-600',
    bgColor: 'bg-blue-50',
  },
  {
    title: 'New This Month',
    value: '0',
    icon: TrendingUp,
    color: 'bg-green-100 text-green-600',
    bgColor: 'bg-green-50',
  },
  {
    title: 'Pending Records',
    value: '0',
    icon: TrendingDown,
    color: 'bg-orange-100 text-orange-600',
    bgColor: 'bg-orange-50',
  },
]



export function DashboardOverview() {
  return (
    <div className="space-y-6 p-6">
      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {statsData.map((stat, index) => (
          <Card key={index} className={`${stat.bgColor} border-0`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">{stat.title}</p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${stat.color}`}>
                  <stat.icon className="h-6 w-6" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Student Registration Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Student Registration Trends</CardTitle>
            <CardDescription>Monthly student enrollment</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl font-bold text-muted-foreground mb-2">📈</div>
                <p className="text-muted-foreground">Student registration chart will appear here</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Track monthly student enrollment trends
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Students Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-center h-[200px]">
              <div className="relative">
                <div className="w-32 h-32 rounded-full bg-gradient-to-r from-purple-400 to-orange-400 flex items-center justify-center">
                  <div className="w-20 h-20 rounded-full bg-white flex items-center justify-center">
                    <span className="text-2xl font-bold">0</span>
                  </div>
                </div>
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2">
                  <span className="text-sm font-medium">Total</span>
                </div>
              </div>
            </div>
            <div className="flex justify-center gap-6 mt-8">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-purple-400"></div>
                <span className="text-sm">Male</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-orange-400"></div>
                <span className="text-sm">Female</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Recent Students */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Students</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-12">
              <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No students yet</h3>
              <p className="text-muted-foreground">
                Start by adding your first student to see them here.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/50 cursor-pointer">
                <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center text-lg">
                  👨‍🎓
                </div>
                <div className="flex-1">
                  <div className="font-medium">Add New Student</div>
                  <div className="text-sm text-muted-foreground">Register a new student in the system</div>
                </div>
              </div>
              <div className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/50 cursor-pointer">
                <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center text-lg">
                  📊
                </div>
                <div className="flex-1">
                  <div className="font-medium">View Reports</div>
                  <div className="text-sm text-muted-foreground">Generate student reports and analytics</div>
                </div>
              </div>
              <div className="flex items-center gap-4 p-3 rounded-lg hover:bg-muted/50 cursor-pointer">
                <div className="w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center text-lg">
                  📋
                </div>
                <div className="flex-1">
                  <div className="font-medium">Manage Records</div>
                  <div className="text-sm text-muted-foreground">Update existing student information</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
