'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase'
import { Loader2 } from 'lucide-react'

export default function LogoutPage() {
  const router = useRouter()

  useEffect(() => {
    const logout = async () => {
      try {
        await supabase.auth.signOut()
        // Clear any local storage
        localStorage.clear()
        sessionStorage.clear()
        // Redirect to login
        router.push('/login?message=You have been logged out successfully')
      } catch (error) {
        console.error('Logout error:', error)
        // Force redirect even if logout fails
        router.push('/login')
      }
    }

    logout()
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <p className="text-muted-foreground">Logging out...</p>
      </div>
    </div>
  )
}
