import { logout } from './actions'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LogOut } from 'lucide-react'

export default function LogoutPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <Card>
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <LogOut className="h-8 w-8 text-blue-600" />
              </div>
            </div>
            <CardTitle>Sign Out</CardTitle>
            <CardDescription>
              Are you sure you want to sign out of your account?
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center space-y-4">
            <form action={logout}>
              <Button type="submit" className="w-full">
                Sign Out
              </Button>
            </form>
            <p className="text-xs text-gray-500">
              You will be redirected to the login page.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
