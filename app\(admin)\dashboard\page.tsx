import React from 'react'
import { redirect } from 'next/navigation'
import { createClient } from '@/utils/supabase/server'
import { DashboardOverview } from '@/components/DashboardOverview'

export default async function DashboardPage() {
  const supabase = await createClient()

  // Check authentication on the server side
  const { data: { user }, error } = await supabase.auth.getUser()

  if (error || !user) {
    redirect('/login')
  }

  // Get user's school information
  const { data: schoolAdmin } = await supabase
    .from('school_admins')
    .select(`
      school_id,
      schools!inner (
        id,
        name,
        email
      )
    `)
    .eq('user_id', user.id)
    .single()

  const schoolName = schoolAdmin?.schools?.name || 'Your School'

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Welcome to Next Gen School Management System - {schoolName}
        </p>
        <p className="text-sm text-gray-500">
          Logged in as: {user.email}
        </p>
      </div>
      <DashboardOverview />
    </div>
  )
}
