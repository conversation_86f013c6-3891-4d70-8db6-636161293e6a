# School Management App - First Feature Development Plan

This document outlines why and how to build the **Student Information Management** feature first for the school management app, which includes an admin panel (React, JavaScript), student/teacher mobile apps (React Native with Expo), Supabase backend, and Zustand state management. The feature is prioritized for its foundational role, enabling core functionality for students, teachers, and admins, and supporting subsequent features. The plan aligns with Phase 1 (Core Functionality) of the previously outlined phase-wise development plan, includes implementation details, test cases, and cybersecurity checks to prevent attacks, and targets completion within the first 4 weeks of development.

## Why Build Student Information Management First?
- **Foundational Feature:** It enables the management of student records (e.g., name, email, grade, attendance), which is critical for all user types:
  - **Students:** View their own records (read-only).
  - **Teachers:** Update grades and attendance for students.
  - **Admins:** Perform full CRUD operations (create, read, update, delete) to manage student data, including adding students as clarified.
- **Dependency for Other Features:** Features like attendance tracking, gradebook, and communication rely on student records being available in the system.
- **High Value for School Management:** Centralizing student data streamlines administrative tasks, ensures data accuracy, and supports compliance with GDPR/FERPA.
- **MVP Requirement:** As part of Phase 1 (Core Functionality), it ensures an early usable product for testing and feedback.
- **Technical Simplicity:** Implementing this feature first establishes the Supabase database, authentication, and RLS, setting up the backend for subsequent features.

## Feature Scope
- **Students (React Native Expo App):**
  - View personal details (name, email, grade, attendance) in a read-only profile.
- **Teachers (React Native Expo App):**
  - Update student grades and attendance records for their classes.
- **Admins (React Web App):**
  - Full CRUD access to student records (add, view, update, delete).
  - Manage student user accounts via Supabase Auth (e.g., create accounts with temporary passwords).
- **Key Functionalities:**
  - Store and manage student data (personal details, academic records, attendance).
  - Role-based access control (RLS) to restrict permissions.
  - Input validation and sanitization to prevent XSS/SQL injection.
  - Data encryption for sensitive fields (e.g., email, medical info).
  - Audit logging for admin actions (e.g., adding students).

## Implementation Plan (Phase 1, Weeks 1-4)
This feature is implemented in the first two sprints of Phase 1 (Core Functionality) to deliver a functional MVP component within 4 weeks.

### Sprint 1 (Weeks 1-2): Backend Setup and Admin CRUD
- **Objective:** Set up Supabase backend and implement admin functionality for adding and managing students.
- **Tasks:**
  1. **Supabase Setup:**
     - Create Supabase project and initialize PostgreSQL table `students`:
       ```sql
       CREATE TABLE students (
         id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
         user_id UUID REFERENCES auth.users(id),
         name TEXT NOT NULL,
         email TEXT NOT NULL UNIQUE,
         grade TEXT,
         role TEXT DEFAULT 'student',
         created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
       );
       ```
     - Enable `pgcrypto` for AES-256 encryption of sensitive fields (e.g., email).
     - Configure Supabase Authentication (JWT, Google OAuth) for admin login.
  2. **RLS Policies:**
     - Implement role-based access:
       ```sql
       -- Admin full access
       CREATE POLICY admin_crud ON students
         FOR ALL
         TO authenticated
         USING (auth.role() = 'admin');

       -- Student view own data
       CREATE POLICY student_view ON students
         FOR SELECT
         TO authenticated
         USING (auth.uid() = user_id);

       -- Teacher update grades/attendance
       CREATE POLICY teacher_update ON students
         FOR UPDATE
         TO authenticated
         USING (auth.role() = 'teacher' AND class_id IN (
           SELECT class_id FROM teacher_classes WHERE teacher_id = auth.uid()
         ));
       ```
  3. **Admin Panel (React):**
     - Initialize React project with Tailwind CSS for styling.
     - Create Zustand store for student management:
       ```javascript
       // src/stores/studentStore.js
       import { create } from 'zustand';
       import { createClient } from '@supabase/supabase-js';

       const supabase = createClient('YOUR_SUPABASE_URL', 'YOUR_SUPABASE_KEY');

       export const useStudentStore = create((set) => ({
         students: [],
         fetchStudents: async () => {
           const { data, error } = await supabase
             .from('students')
             .select('id, name, email, grade')
             .eq('role', 'student');
           if (error) throw new Error(error.message);
           set({ students: data });
         },
         addStudent: async (student) => {
           const { error } = await supabase
             .from('students')
             .insert([{ ...student, role: 'student' }]);
           if (error) throw new Error(error.message);
           await useStudentStore.getState().fetchStudents();
         },
         updateStudent: async (id, updates) => {
           const { error } = await supabase
             .from('students')
             .update(updates)
             .eq('id', id);
           if (error) throw new Error(error.message);
           await useStudentStore.getState().fetchStudents();
         },
         deleteStudent: async (id) => {
           const { error } = await supabase
             .from('students')
             .delete()
             .eq('id', id);
           if (error) throw new Error(error.message);
           await useStudentStore.getState().fetchStudents();
         },
       }));
       ```
     - Develop admin panel component for CRUD operations:
       ```javascript
       // src/components/StudentManagement.js
       import React, { useState, useEffect } from 'react';
       import { useStudentStore } from '../stores/studentStore';
       import sanitizeHtml from 'sanitize-html';

       const StudentManagement = () => {
         const { students, fetchStudents, addStudent, updateStudent, deleteStudent } = useStudentStore();
         const [form, setForm] = useState({ name: '', email: '', grade: '' });

         useEffect(() => {
           fetchStudents();
         }, []);

         const handleAdd = async () => {
           const sanitizedForm = {
             name: sanitizeHtml(form.name),
             email: sanitizeHtml(form.email),
             grade: sanitizeHtml(form.grade),
           };
           await addStudent(sanitizedForm);
           setForm({ name: '', email: '', grade: '' });
         };

         return (
           <div className="p-4">
             <h2 className="text-2xl mb-4">Student Management</h2>
             <div className="mb-4">
               <input
                 type="text"
                 placeholder="Name"
                 value={form.name}
                 onChange={(e) => setForm({ ...form, name: e.target.value })}
                 className="border p-2 mr-2"
               />
               <input
                 type="email"
                 placeholder="Email"
                 value={form.email}
                 onChange={(e) => setForm({ ...form, email: e.target.value })}
                 className="border p-2 mr-2"
               />
               <input
                 type="text"
                 placeholder="Grade"
                 value={form.grade}
                 onChange={(e) => setForm({ ...form, grade: e.target.value })}
                 className="border p-2 mr-2"
               />
               <button onClick={handleAdd} className="bg-blue-500 text-white p-2">Add Student</button>
             </div>
             <table className="w-full border">
               <thead>
                 <tr>
                   <th className="border p-2">Name</th>
                   <th className="border p-2">Email</th>
                   <th className="border p-2">Grade</th>
                   <th className="border p-2">Actions</th>
                 </tr>
               </thead>
               <tbody>
                 {students.map((student) => (
                   <tr key={student.id}>
                     <td className="border p-2">{student.name}</td>
                     <td className="border p-2">{student.email}</td>
                     <td className="border p-2">{student.grade}</td>
                     <td className="border p-2">
                       <button
                         onClick={() => updateStudent(student.id, { grade: 'Updated' })}
                         className="bg-yellow-500 text-white p-1 mr-2"
                       >
                         Update
                       </button>
                       <button
                         onClick={() => deleteStudent(student.id)}
                         className="bg-red-500 text-white p-1"
                       >
                         Delete
                       </button>
                     </td>
                   </tr>
                 ))}
               </tbody>
             </table>
           </div>
         );
       };

       export default StudentManagement;
       ```
  4. **Supabase Auth Integration:**
     - Create student user accounts in Supabase Auth when adding a student:
       ```javascript
       // src/stores/studentStore.js (add to addStudent)
       addStudent: async (student) => {
         const { data: user, error: authError } = await supabase.auth.signUp({
           email: student.email,
           password: 'temporary-password', // Send invite link instead in production
         });
         if (authError) throw new Error(authError.message);
         const { error } = await supabase
           .from('students')
           .insert([{ ...student, role: 'student', user_id: user.user.id }]);
         if (error) throw new Error(error.message);
         await useStudentStore.getState().fetchStudents();
       },
       ```
- **Cybersecurity Checks:**
  - **RLS Enforcement:** Ensure only admins can insert/update/delete students.
  - **JWT Authentication:** Validate admin identity for all API calls.
  - **Input Sanitization:** Use `sanitize-html` to prevent XSS (e.g., `<script>alert('xss')</script>`).
  - **Data Encryption:** Enable `pgcrypto` for sensitive fields.
- **Test Cases:**
  1. **Admin Adds Student**
     - **Steps:** Log in as admin, enter valid student data (name, email, grade), submit.
     - **Expected Result:** Student added to `students` table, user account created in Supabase Auth.
  2. **Unauthorized Access**
     - **Steps:** Log in as teacher/student, attempt `POST /students` API call.
     - **Expected Result:** 403 Forbidden error.
  3. **XSS Prevention**
     - **Steps:** Submit `<script>alert('xss')</script>` as student name.
     - **Expected Result:** Input sanitized, no script execution.
  4. **Data Encryption**
     - **Steps:** Query `students` table, check email field.
     - **Expected Result:** Data encrypted, unreadable without key.

### Sprint 2 (Weeks 3-4): Student and Teacher Interfaces
- **Objective:** Implement student and teacher interfaces for viewing and updating student data, deploy MVP component.
- **Tasks:**
  1. **Student Interface (React Native Expo):**
     - Develop profile screen to display student data:
       ```javascript
       // src/screens/StudentProfile.js
       import React, { useEffect } from 'react';
       import { View, Text } from 'react-native';
       import { useStudentStore } from '../stores/studentStore';
       import { supabase } from '../supabase';

       const StudentProfile = () => {
         const { students, fetchStudents } = useStudentStore();

         useEffect(() => {
           fetchStudents();
         }, []);

         const currentStudent = students.find((s) => s.user_id === supabase.auth.user()?.id);

         return (
           <View className="p-4">
             <Text className="text-2xl mb-4">My Profile</Text>
             {currentStudent ? (
               <>
                 <Text>Name: {currentStudent.name}</Text>
                 <Text>Email: {currentStudent.email}</Text>
                 <Text>Grade: {currentStudent.grade}</Text>
               </>
             ) : (
               <Text>Loading...</Text>
             )}
           </View>
         );
       };

       export default StudentProfile;
       ```
  2. **Teacher Interface (React Native Expo):**
     - Develop screen to update student grades/attendance:
       ```javascript
       // src/screens/TeacherStudentManagement.js
       import React, { useEffect, useState } from 'react';
       import { View, Text, TextInput, Button } from 'react-native';
       import { useStudentStore } from '../stores/studentStore';

       const TeacherStudentManagement = () => {
         const { students, fetchStudents, updateStudent } = useStudentStore();
         const [grade, setGrade] = useState('');

         useEffect(() => {
           fetchStudents();
         }, []);

         const handleUpdateGrade = async (studentId) => {
           await updateStudent(studentId, { grade });
           setGrade('');
         };

         return (
           <View className="p-4">
             <Text className="text-2xl mb-4">Manage Students</Text>
             {students.map((student) => (
               <View key={student.id} className="mb-2">
                 <Text>{student.name} - Current Grade: {student.grade}</Text>
                 <TextInput
                   placeholder="New Grade"
                   value={grade}
                   onChangeText={setGrade}
                   className="border p-2"
                 />
                 <Button
                   title="Update Grade"
                   onPress={() => handleUpdateGrade(student.id)}
                   color="#3b82f6"
                 />
               </View>
             ))}
           </View>
         );
       };

       export default TeacherStudentManagement;
       ```
  3. **Deployment:**
     - Deploy admin panel to Vercel.
     - Publish mobile app MVP via Expo EAS for testing.
  4. **Testing:**
     - Run unit tests (Jest) for React and React Native components.
     - Test integration with Supabase APIs.
     - Conduct User Acceptance Testing (UAT) with sample admin, teacher, and student users.
- **Cybersecurity Checks:**
  - **Secure API Endpoints:** Restrict `students` table access to authenticated users via RLS.
  - **Session Management:** Use Zustand with Expo SecureStore for secure session handling.
  - **Rate Limiting:** Apply rate limiting to `SELECT` and `UPDATE` API calls to prevent abuse.
- **Test Cases:**
  1. **Student Views Profile**
     - **Steps:** Log in as student, navigate to profile screen.
     - **Expected Result:** Displays correct name, email, grade; no edit options.
  2. **Teacher Updates Grade**
     - **Steps:** Log in as teacher, update student’s grade.
     - **Expected Result:** Grade updated in database, visible to student.
  3. **Rate Limiting**
     - **Steps:** Send 100 `SELECT /students` API calls in 1 minute.
     - **Expected Result:** 429 Too Many Requests error.
  4. **Session Security**
     - **Steps:** Log in as student, wait 15 minutes, attempt action.
     - **Expected Result:** Session expired, user logged out.

**Deliverables:**
- Supabase `students` table with RLS policies.
- Admin panel with student CRUD functionality.
- Student profile screen in mobile app.
- Teacher grade/attendance update screen in mobile app.
- Unit and integration test reports.
- Deployed MVP component (Vercel for admin, Expo EAS for mobile).

## Cybersecurity Checks
The following cybersecurity measures from the previous response are implemented to protect the Student Information Management feature:
1. **Role-Based Access Control (RLS):**
   - Only admins can insert/update/delete students; students can only view their own data; teachers can update grades/attendance for their classes.
   - **Validation:** Test unauthorized actions (e.g., student attempting `UPDATE /students`).
2. **JWT Authentication:**
   - Verify user identity for all API calls using Supabase Auth JWT tokens.
   - **Validation:** Test with invalid/expired tokens, expect 401 errors.
3. **Input Sanitization:**
   - Use `sanitize-html` in admin panel to prevent XSS attacks.
   - **Validation:** Submit malicious input (e.g., `<script>alert('xss')</script>`), expect sanitization.
4. **Data Encryption:**
   - Encrypt sensitive fields (e.g., email) using Supabase’s `pgcrypto`.
   - **Validation:** Query database, verify encrypted fields are unreadable.
5. **Audit Logging:**
   - Log admin actions (e.g., adding students) in Supabase audit logs for GDPR/FERPA compliance.
   - **Validation:** Check logs for admin action entries.

## Implementation Considerations
- **Priority:** Building Student Information Management first establishes the core data structure, enabling other features (e.g., attendance, grades) in subsequent sprints.
- **Scalability:** Supabase’s PostgreSQL supports thousands of student records; add indexes on `user_id` and `email` for performance.
- **Security:** RLS, JWT, and encryption ensure data protection from the start.
- **Usability:** Design intuitive interfaces (admin form, student profile, teacher update screen) with Tailwind CSS for consistency.
- **User Training:** Provide admins with a guide on adding students, teachers with instructions for updating grades/attendance.

## Benefits
- **Admins:** Centralized control over student data, streamlined onboarding.
- **Teachers:** Immediate ability to manage grades/attendance, critical for daily operations.
- **Students:** Access to personal records, enhancing engagement and transparency.
- **Foundation for MVP:** Enables rapid progression to other Phase 1 features (e.g., attendance tracking, gradebook).

## Next Steps
- **Start Development:** Initialize Supabase, React, and React Native projects, and implement Student Information Management in Weeks 1-4.
- **Testing:** Run test cases after each sprint to validate functionality and security.
- **Feedback:** Collect feedback from sample admins, teachers, and students post-Sprint 2 to refine interfaces.
- **Proceed to Next Features:** In Weeks 5-8, build attendance tracking and gradebook, leveraging the `students` table.

By building the **Student Information Management** feature first, the app establishes a secure, scalable foundation for all user types, ensuring an effective MVP and smooth progression to subsequent features.