import sanitizeHtml from 'sanitize-html'

/**
 * Security utilities for input validation and sanitization
 * Following the blueprint's cybersecurity requirements
 */

// Sanitization configuration for different input types
const sanitizeOptions = {
  // For general text inputs (names, etc.)
  text: {
    allowedTags: [],
    allowedAttributes: {},
    disallowedTagsMode: 'discard',
  },
  // For email inputs
  email: {
    allowedTags: [],
    allowedAttributes: {},
    disallowedTagsMode: 'discard',
  }
}

/**
 * Sanitize text input to prevent XSS attacks
 * @param input - The input string to sanitize
 * @param type - The type of input (text, email)
 * @returns Sanitized string
 */
export const sanitizeInput = (input: string, type: 'text' | 'email' = 'text'): string => {
  if (typeof input !== 'string') {
    return ''
  }
  
  return sanitizeHtml(input, sanitizeOptions[type]).trim()
}

/**
 * Validate email format
 * @param email - Email string to validate
 * @returns Boolean indicating if email is valid
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate required fields
 * @param fields - Object with field names and values
 * @returns Object with validation results
 */
export const validateRequiredFields = (fields: Record<string, string>) => {
  const errors: Record<string, string> = {}
  
  Object.entries(fields).forEach(([key, value]) => {
    if (!value || value.trim().length === 0) {
      errors[key] = `${key.charAt(0).toUpperCase() + key.slice(1)} is required`
    }
  })
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * Validate student form data
 * @param data - Student form data
 * @returns Validation result with sanitized data
 */
export const validateStudentData = (data: { name: string; email: string; grade: string }) => {
  // Sanitize inputs
  const sanitized = {
    name: sanitizeInput(data.name, 'text'),
    email: sanitizeInput(data.email, 'email'),
    grade: sanitizeInput(data.grade, 'text')
  }
  
  // Validate required fields
  const requiredValidation = validateRequiredFields(sanitized)
  if (!requiredValidation.isValid) {
    return {
      isValid: false,
      errors: requiredValidation.errors,
      data: sanitized
    }
  }
  
  // Validate email format
  if (!validateEmail(sanitized.email)) {
    return {
      isValid: false,
      errors: { email: 'Please enter a valid email address' },
      data: sanitized
    }
  }
  
  // Validate name length
  if (sanitized.name.length < 2) {
    return {
      isValid: false,
      errors: { name: 'Name must be at least 2 characters long' },
      data: sanitized
    }
  }
  
  // Validate grade format (basic validation)
  if (sanitized.grade.length < 1) {
    return {
      isValid: false,
      errors: { grade: 'Grade is required' },
      data: sanitized
    }
  }
  
  return {
    isValid: true,
    errors: {},
    data: sanitized
  }
}

/**
 * Rate limiting utility (basic implementation)
 * In production, this should be implemented server-side
 */
class RateLimiter {
  private requests: Map<string, number[]> = new Map()
  private readonly maxRequests: number
  private readonly windowMs: number
  
  constructor(maxRequests: number = 10, windowMs: number = 60000) {
    this.maxRequests = maxRequests
    this.windowMs = windowMs
  }
  
  isAllowed(identifier: string): boolean {
    const now = Date.now()
    const requests = this.requests.get(identifier) || []
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < this.windowMs)
    
    if (validRequests.length >= this.maxRequests) {
      return false
    }
    
    validRequests.push(now)
    this.requests.set(identifier, validRequests)
    return true
  }
}

export const rateLimiter = new RateLimiter()

/**
 * Error handling utility
 * @param error - Error object or string
 * @returns Formatted error message
 */
export const handleError = (error: unknown): string => {
  if (error instanceof Error) {
    // Don't expose sensitive error details in production
    if (process.env.NODE_ENV === 'production') {
      // Log the full error for debugging
      console.error('Application error:', error)
      
      // Return generic message for common errors
      if (error.message.includes('duplicate key')) {
        return 'A record with this information already exists'
      }
      if (error.message.includes('permission')) {
        return 'You do not have permission to perform this action'
      }
      if (error.message.includes('network')) {
        return 'Network error. Please check your connection and try again'
      }
      
      return 'An unexpected error occurred. Please try again'
    }
    
    return error.message
  }
  
  return 'An unexpected error occurred'
}
