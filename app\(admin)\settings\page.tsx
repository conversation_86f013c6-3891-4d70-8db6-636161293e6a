'use client'

import React, { useState, useEffect } from 'react'
import { AdminLayout } from '@/components/AdminLayout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { useAuth } from '@/contexts/AuthContext'
import { useSchool } from '@/contexts/SchoolContext'
import { supabase } from '@/lib/supabase'
import {
  Settings,
  School,
  User,
  Mail,
  Phone,
  MapPin,
  Globe,
  Save,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  Lock
} from 'lucide-react'

interface SchoolFormData {
  name: string
  email: string
  phone: string
  address: string
  website: string
}

interface AdminFormData {
  name: string
  email: string
  currentPassword: string
  newPassword: string
  confirmPassword: string
}

export default function SettingsPage() {
  const { user } = useAuth()
  const { school, refreshSchool } = useSchool()

  const [schoolData, setSchoolData] = useState<SchoolFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    website: ''
  })

  const [adminData, setAdminData] = useState<AdminFormData>({
    name: '',
    email: '',
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })

  const [loading, setLoading] = useState(false)
  const [schoolLoading, setSchoolLoading] = useState(false)
  const [adminLoading, setAdminLoading] = useState(false)
  const [message, setMessage] = useState('')
  const [error, setError] = useState('')
  const [showPasswords, setShowPasswords] = useState(false)
  const [passwordErrors, setPasswordErrors] = useState<string[]>([])

  // Load initial data
  useEffect(() => {
    if (school) {
      setSchoolData({
        name: school.name || '',
        email: school.email || '',
        phone: school.phone || '',
        address: school.address || '',
        website: school.website || ''
      })
    }

    if (user) {
      setAdminData(prev => ({
        ...prev,
        name: user.user_metadata?.name || '',
        email: user.email || ''
      }))
    }
  }, [school, user])

  // Password validation function
  const validatePassword = (password: string): string[] => {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }
    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }

    return errors
  }

  const handleSchoolChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSchoolData({
      ...schoolData,
      [e.target.name]: e.target.value
    })
  }

  const handleAdminChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    setAdminData({
      ...adminData,
      [name]: value
    })

    // Validate new password in real-time
    if (name === 'newPassword') {
      const errors = validatePassword(value)
      setPasswordErrors(errors)
    }
  }

  const handleSchoolSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!school) return

    setSchoolLoading(true)
    setError('')
    setMessage('')

    try {
      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(schoolData.email)) {
        throw new Error('Please enter a valid email address')
      }

      const { error } = await supabase
        .from('schools')
        .update({
          name: schoolData.name,
          email: schoolData.email,
          phone: schoolData.phone || null,
          address: schoolData.address || null,
          website: schoolData.website || null,
          updated_at: new Date().toISOString()
        })
        .eq('id', school.id)

      if (error) throw error

      await refreshSchool()
      setMessage('School information updated successfully!')

      setTimeout(() => setMessage(''), 5000)
    } catch (err: any) {
      setError(err.message || 'Failed to update school information')
    } finally {
      setSchoolLoading(false)
    }
  }

  const handleAdminSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    setAdminLoading(true)
    setError('')
    setMessage('')

    try {
      // If changing password, validate it
      if (adminData.newPassword) {
        if (!adminData.currentPassword) {
          throw new Error('Current password is required to change password')
        }

        if (adminData.newPassword !== adminData.confirmPassword) {
          throw new Error('New passwords do not match')
        }

        const passwordValidationErrors = validatePassword(adminData.newPassword)
        if (passwordValidationErrors.length > 0) {
          throw new Error('Password does not meet security requirements: ' + passwordValidationErrors.join(', '))
        }

        // Update password
        const { error: passwordError } = await supabase.auth.updateUser({
          password: adminData.newPassword
        })

        if (passwordError) throw passwordError
      }

      // Update user metadata (name)
      const { error: metadataError } = await supabase.auth.updateUser({
        data: {
          name: adminData.name
        }
      })

      if (metadataError) throw metadataError

      // Update email if changed
      if (adminData.email !== user.email) {
        const { error: emailError } = await supabase.auth.updateUser({
          email: adminData.email
        })

        if (emailError) throw emailError
      }

      setMessage('Admin profile updated successfully!')

      // Clear password fields
      setAdminData(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }))

      setTimeout(() => setMessage(''), 5000)
    } catch (err: any) {
      setError(err.message || 'Failed to update admin profile')
    } finally {
      setAdminLoading(false)
    }
  }

  return (
    <AdminLayout title="Settings">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your school profile and admin account settings
          </p>
        </div>

        {/* Success/Error Messages */}
        {message && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">{message}</AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <div className="grid gap-6 lg:grid-cols-2">
          {/* School Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <School className="h-5 w-5 text-blue-600" />
                School Information
              </CardTitle>
              <CardDescription>
                Update your school's basic information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSchoolSubmit} className="space-y-4">
                <div>
                  <label htmlFor="schoolName" className="block text-sm font-medium text-gray-700 mb-2">
                    School Name *
                  </label>
                  <div className="relative">
                    <School className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="schoolName"
                      name="name"
                      type="text"
                      placeholder="Enter school name"
                      value={schoolData.name}
                      onChange={handleSchoolChange}
                      className="pl-10"
                      required
                      disabled={schoolLoading}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="schoolEmail" className="block text-sm font-medium text-gray-700 mb-2">
                    School Email *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="schoolEmail"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={schoolData.email}
                      onChange={handleSchoolChange}
                      className="pl-10"
                      required
                      disabled={schoolLoading}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="schoolPhone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number
                  </label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="schoolPhone"
                      name="phone"
                      type="tel"
                      placeholder="+****************"
                      value={schoolData.phone}
                      onChange={handleSchoolChange}
                      className="pl-10"
                      disabled={schoolLoading}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="schoolAddress" className="block text-sm font-medium text-gray-700 mb-2">
                    Address
                  </label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="schoolAddress"
                      name="address"
                      type="text"
                      placeholder="123 Education Street, City, State"
                      value={schoolData.address}
                      onChange={handleSchoolChange}
                      className="pl-10"
                      disabled={schoolLoading}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="schoolWebsite" className="block text-sm font-medium text-gray-700 mb-2">
                    Website
                  </label>
                  <div className="relative">
                    <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="schoolWebsite"
                      name="website"
                      type="url"
                      placeholder="https://www.yourschool.edu"
                      value={schoolData.website}
                      onChange={handleSchoolChange}
                      className="pl-10"
                      disabled={schoolLoading}
                    />
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={schoolLoading}
                >
                  {schoolLoading ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      Updating School...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Update School Information
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Admin Profile */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-green-600" />
                Admin Profile
              </CardTitle>
              <CardDescription>
                Update your personal information and change your password
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAdminSubmit} className="space-y-4">
                <div>
                  <label htmlFor="adminName" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="adminName"
                      name="name"
                      type="text"
                      placeholder="Enter your full name"
                      value={adminData.name}
                      onChange={handleAdminChange}
                      className="pl-10"
                      required
                      disabled={adminLoading}
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="adminEmail" className="block text-sm font-medium text-gray-700 mb-2">
                    Email Address *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="adminEmail"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={adminData.email}
                      onChange={handleAdminChange}
                      className="pl-10"
                      required
                      disabled={adminLoading}
                    />
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Changing email will require verification
                  </p>
                </div>

                <Separator className="my-6" />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">Change Password</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowPasswords(!showPasswords)}
                    >
                      {showPasswords ? (
                        <>
                          <EyeOff className="h-4 w-4 mr-1" />
                          Hide
                        </>
                      ) : (
                        <>
                          <Eye className="h-4 w-4 mr-1" />
                          Show
                        </>
                      )}
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Leave password fields empty if you don't want to change your password
                  </p>

                  <div>
                    <label htmlFor="currentPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      Current Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="currentPassword"
                        name="currentPassword"
                        type={showPasswords ? "text" : "password"}
                        placeholder="Enter current password"
                        value={adminData.currentPassword}
                        onChange={handleAdminChange}
                        className="pl-10"
                        disabled={adminLoading}
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      New Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="newPassword"
                        name="newPassword"
                        type={showPasswords ? "text" : "password"}
                        placeholder="Enter new password"
                        value={adminData.newPassword}
                        onChange={handleAdminChange}
                        className="pl-10"
                        disabled={adminLoading}
                      />
                    </div>

                    {/* Password Requirements */}
                    {adminData.newPassword && (
                      <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                        <h5 className="text-xs font-medium text-blue-800 mb-1">Password Requirements:</h5>
                        <ul className="text-xs text-blue-700 space-y-1">
                          <li className={`flex items-center ${adminData.newPassword.length >= 8 ? 'text-green-600' : ''}`}>
                            <span className="mr-1">{adminData.newPassword.length >= 8 ? '✓' : '•'}</span>
                            At least 8 characters
                          </li>
                          <li className={`flex items-center ${/[a-z]/.test(adminData.newPassword) ? 'text-green-600' : ''}`}>
                            <span className="mr-1">{/[a-z]/.test(adminData.newPassword) ? '✓' : '•'}</span>
                            One lowercase letter
                          </li>
                          <li className={`flex items-center ${/[A-Z]/.test(adminData.newPassword) ? 'text-green-600' : ''}`}>
                            <span className="mr-1">{/[A-Z]/.test(adminData.newPassword) ? '✓' : '•'}</span>
                            One uppercase letter
                          </li>
                          <li className={`flex items-center ${/\d/.test(adminData.newPassword) ? 'text-green-600' : ''}`}>
                            <span className="mr-1">{/\d/.test(adminData.newPassword) ? '✓' : '•'}</span>
                            One number
                          </li>
                          <li className={`flex items-center ${/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(adminData.newPassword) ? 'text-green-600' : ''}`}>
                            <span className="mr-1">{/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(adminData.newPassword) ? '✓' : '•'}</span>
                            One special character
                          </li>
                        </ul>
                      </div>
                    )}

                    {/* Password Errors */}
                    {passwordErrors.length > 0 && (
                      <div className="mt-2">
                        {passwordErrors.map((error, index) => (
                          <p key={index} className="text-sm text-red-600 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {error}
                          </p>
                        ))}
                      </div>
                    )}
                  </div>

                  <div>
                    <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                      Confirm New Password
                    </label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type={showPasswords ? "text" : "password"}
                        placeholder="Confirm new password"
                        value={adminData.confirmPassword}
                        onChange={handleAdminChange}
                        className="pl-10"
                        disabled={adminLoading}
                      />
                    </div>
                    {adminData.confirmPassword && adminData.newPassword !== adminData.confirmPassword && (
                      <p className="mt-1 text-sm text-red-600 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Passwords do not match
                      </p>
                    )}
                    {adminData.confirmPassword && adminData.newPassword === adminData.confirmPassword && adminData.confirmPassword.length > 0 && (
                      <p className="mt-1 text-sm text-green-600 flex items-center">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Passwords match
                      </p>
                    )}
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full"
                  disabled={adminLoading || (adminData.newPassword && passwordErrors.length > 0)}
                >
                  {adminLoading ? (
                    <>
                      <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                      Updating Profile...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Update Admin Profile
                    </>
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>

        {/* Additional Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-purple-600" />
              Account Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">School ID</h4>
                <p className="text-sm text-gray-600 font-mono bg-gray-50 p-2 rounded">
                  {school?.id || 'Loading...'}
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Account Created</h4>
                <p className="text-sm text-gray-600">
                  {school?.created_at ? new Date(school.created_at).toLocaleDateString() : 'Loading...'}
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Subscription Plan</h4>
                <p className="text-sm text-gray-600 capitalize">
                  {school?.subscription_plan || 'Basic'}
                </p>
              </div>
              <div className="space-y-2">
                <h4 className="text-sm font-medium text-gray-900">Account Status</h4>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                  school?.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {school?.is_active ? 'Active' : 'Inactive'}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
