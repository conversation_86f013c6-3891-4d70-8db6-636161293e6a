'use client'

import React from 'react'
import { ChevronDown } from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'
import { useSchool } from '@/contexts/SchoolContext'
import {
  SidebarTrigger,
} from '@/components/ui/sidebar'

interface AdminHeaderProps {
  title?: string
}

export function AdminHeader({ title = "Admin Dashboard" }: AdminHeaderProps) {
  const { user } = useAuth()
  const { school } = useSchool()

  // Get user initials for avatar
  const getUserInitials = () => {
    const name = user?.user_metadata?.name || user?.email || 'User'
    return name.split(' ').map((n: string) => n[0]).join('').toUpperCase().slice(0, 2)
  }

  return (
    <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
      <SidebarTrigger className="-ml-1" />

      <div className="flex flex-1 items-center gap-4">
        <h1 className="text-lg font-semibold">{title}</h1>

        <div className="ml-auto flex items-center gap-4">
          {/* User Profile */}
          <div className="flex items-center gap-2 cursor-pointer hover:bg-gray-50 rounded-lg p-2 transition-colors">
            <div className="h-8 w-8 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center">
              <span className="text-white text-sm font-medium">
                {getUserInitials()}
              </span>
            </div>
            <div className="hidden md:block">
              <div className="text-sm font-medium">
                {user?.user_metadata?.name || 'Admin User'}
              </div>
              <div className="text-xs text-muted-foreground">
                {school?.name || 'School Admin'}
              </div>
            </div>
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          </div>
        </div>
      </div>
    </header>
  )
}
